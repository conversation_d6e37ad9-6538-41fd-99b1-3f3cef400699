"use client";
import { useEffect, useState } from "react";
import Typst<PERSON>oreViewer from "../components/TypstCoreViewer";

export const metadata = {
  title: "Typst Demo",
};

export default function Page() {
  const [artifactBlob, setArtifactBlob] = useState<Blob | null>(null);
  const artifactPath = "/typst/demo.artifact.sir.in";

  useEffect(() => {
    let alive = true;
    (async () => {
      try {
        const res = await fetch(artifactPath);
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        const blob = await res.blob();
        if (alive) setArtifactBlob(blob);
      } catch (e) {
        console.error("Failed to load demo artifact:", e);
      }
    })();
    return () => { alive = false };
  }, []);

  return (
    <div style={{ padding: 16 }}>
      <h1 style={{ fontSize: 20, fontWeight: 600 }}>Typst Preview</h1>
      <p style={{ color: "#555" }}>Rendering artifact from: {artifactPath}</p>
      <div style={{ border: "1px solid #eee", marginTop: 12, padding: 8 }}>
        {artifactBlob ? (
          <TypstCoreViewer artifactBlob={artifactBlob} />
        ) : (
          <div>Loading demo artifact…</div>
        )}
      </div>
    </div>
  );
}
